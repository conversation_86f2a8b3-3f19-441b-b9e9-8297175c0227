import CrateServices
import Factory
import Swift<PERSON>

@MainActor
final public class AppSettingsViewModel: ObservableObject {
  @Published public var serverURL: String =
    UserDefaults.standard.serverURL ?? "https://api.cratenfc.com"
  @Published public var isClearingCache = false
  @Published public var showClearCacheAlert = false
  @Published public var clearCacheMessage = ""

  private let crateActor: CrateActor

  public init() {
    self.crateActor = Container.shared.crateActor()
  }

  public func clearCache() async {
    await MainActor.run {
      isClearingCache = true
    }

    do {
      try await crateActor.clearAllUserData()
      await MainActor.run {
        clearCacheMessage = "✅ Cache cleared successfully"
        showClearCacheAlert = true
        isClearingCache = false
      }
    } catch {
      await MainActor.run {
        clearCacheMessage = "❌ Failed to clear cache: \(error.localizedDescription)"
        showClearCacheAlert = true
        isClearingCache = false
      }
    }
  }
}
