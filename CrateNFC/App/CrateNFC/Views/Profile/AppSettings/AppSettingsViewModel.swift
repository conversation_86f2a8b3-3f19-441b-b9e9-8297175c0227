import CrateServices
import Factory
import SwiftUI

@MainActor
final public class AppSettingsViewModel: ObservableObject {
  @Published public var serverURL: String =
    UserDefaults.standard.serverURL ?? "https://api.cratenfc.com"
  @Published public var isClearingCache = false
  @Published public var showClearCacheAlert = false
  @Published public var clearCacheMessage = ""
  @Published public var dataCounts = DataCounts()

  private let crateActor: CrateActor

  public init() {
    self.crateActor = Container.shared.crateActor()
    Task {
      await refreshDataCounts()
    }
  }

  public struct DataCounts {
    var content: Int = 0
    var trendingContent: Int = 0
    var recentCollections: Int = 0
    var collections: Int = 0
  }

  public func refreshDataCounts() async {
    do {
      let counts = try await crateActor.getDataCounts()
      await MainActor.run {
        self.dataCounts = DataCounts(
          content: counts.content,
          trendingContent: counts.trendingContent,
          recentCollections: counts.recentCollections,
          collections: counts.collections
        )
      }
    } catch {
      print("Error fetching data counts: \(error.localizedDescription)")
    }
  }

  public func clearCache() async {
    await MainActor.run {
      isClearingCache = true
    }

    do {
      try await crateActor.clearAllUserData()
      await MainActor.run {
        clearCacheMessage = "✅ Cache cleared successfully"
        showClearCacheAlert = true
        isClearingCache = false
        // Reset counts to zero after clearing
        dataCounts = DataCounts()
      }
    } catch {
      await MainActor.run {
        clearCacheMessage = "❌ Failed to clear cache: \(error.localizedDescription)"
        showClearCacheAlert = true
        isClearingCache = false
      }
    }
  }
}
