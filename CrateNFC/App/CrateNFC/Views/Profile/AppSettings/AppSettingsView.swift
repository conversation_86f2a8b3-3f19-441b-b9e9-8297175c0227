import Foundation
import SwiftUI

struct AppSettingsView: View {
  private let appVersion: String =
    Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "1.0.0"
  private let buildNumber: String =
    Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "1"
  let standardURLs = [
    URL(string: "https://api.cratenfc.com")!,
    URL(string: "http://localhost:8000")!
  ]
  @StateObject private var viewModel: AppSettingsViewModel
  public init() {
    _viewModel = StateObject(wrappedValue: AppSettingsViewModel())
  }

  var body: some View {
    NavigationView {
      Form {
        getServerSettingsSection()
        getDataManagementSection()
        getAppInformationSection()
      }
      .navigationTitle("Settings")
      .padding()
      .alert("Clear Cache", isPresented: $viewModel.showClearCacheAlert) {
        Button("OK") {}
      } message: {
        Text(viewModel.clearCacheMessage)
      }
    }
  }

  private var copyrightText: String {
    let currentYear = Calendar.current.component(.year, from: Date())
    return "©️ \(currentYear) Lilrobo Inc. All rights reserved."
  }

  // MARK: - View Components

  @ViewBuilder
  private func getServerSettingsSection() -> some View {
    Section(header: Text("Server Settings")) {
      getServerURLPicker()
      getServerURLTextField()
      getCustomServerWarning()
    }
  }

  @ViewBuilder
  private func getDataManagementSection() -> some View {
    Section(header: Text("Data Management")) {
      getDataOverview()
      getRefreshDataButton()
      getClearCacheButton()
    }
  }

  private func getAppInformationSection() -> some View {
    Section {
      VStack(alignment: .center, spacing: 8) {
        getVersionInfo()
        getCopyrightInfo()
        getWebsiteLink()
      }
      .frame(maxWidth: .infinity)
    }
  }

  // MARK: - Helper Views

  @ViewBuilder
  private func getServerURLPicker() -> some View {
    Picker("Server URL", selection: $viewModel.serverURL) {
      ForEach(standardURLs, id: \.absoluteString) { serverURL in
        Text(serverURL.absoluteString).tag(serverURL.absoluteString)
      }
    }
    .pickerStyle(SegmentedPickerStyle())
    .onChange(of: viewModel.serverURL) { _, newValue in
      UserDefaults.standard.serverURL = newValue
      print("📡 Server URL changed to: \(newValue)")
    }
  }

  @ViewBuilder
  private func getServerURLTextField() -> some View {
    TextField("Server URL", text: $viewModel.serverURL)
      .textInputAutocapitalization(.never)
      .autocorrectionDisabled()
  }

  @ViewBuilder
  private func getCustomServerWarning() -> some View {
    let standardURLStrings = standardURLs.map { $0.absoluteString }
    if !standardURLStrings.contains(viewModel.serverURL) {
      Text("⚠️ Using custom server")
        .font(.footnote)
        .foregroundColor(.orange)
    }
  }

  @ViewBuilder
  private func getVersionInfo() -> some View {
    Text("App Version: \(appVersion) (\(buildNumber))")
      .font(.footnote)
      .multilineTextAlignment(.center)
  }

  @ViewBuilder
  private func getCopyrightInfo() -> some View {
    Text(copyrightText)
      .font(.footnote)
      .multilineTextAlignment(.center)
  }

  @ViewBuilder
  private func getWebsiteLink() -> some View {
    if let websiteURL = URL(string: "https://lilrobo.xyz") {
      Link("Visit our website", destination: websiteURL)
        .font(.footnote)
        .foregroundColor(.blue)
    }
  }

  @ViewBuilder
  private func getDataOverview() -> some View {
    VStack(alignment: .leading, spacing: 8) {
      Text("Stored Data")
        .font(.headline)
        .padding(.bottom, 4)

      HStack {
        Text("Content:")
        Spacer()
        Text("\(viewModel.dataCounts.content)")
          .foregroundColor(.secondary)
      }

      HStack {
        Text("Trending Content:")
        Spacer()
        Text("\(viewModel.dataCounts.trendingContent)")
          .foregroundColor(.secondary)
      }

      HStack {
        Text("Recent Collections:")
        Spacer()
        Text("\(viewModel.dataCounts.recentCollections)")
          .foregroundColor(.secondary)
      }

      HStack {
        Text("Collections:")
        Spacer()
        Text("\(viewModel.dataCounts.collections)")
          .foregroundColor(.secondary)
      }
    }
    .padding(.vertical, 4)
  }

  @ViewBuilder
  private func getRefreshDataButton() -> some View {
    Button(action: {
      Task {
        await viewModel.refreshDataCounts()
      }
    }) {
      HStack {
        Image(systemName: "arrow.clockwise")
          .foregroundColor(.blue)
        Text("Refresh Data Counts")
          .foregroundColor(.blue)
        Spacer()
      }
    }
  }

  @ViewBuilder
  private func getClearCacheButton() -> some View {
    Button(action: {
      Task {
        await viewModel.clearCache()
      }
    }) {
      HStack {
        Image(systemName: "trash")
          .foregroundColor(.red)
        Text("Clear Cache")
          .foregroundColor(.red)
        Spacer()
        if viewModel.isClearingCache {
          ProgressView()
            .scaleEffect(0.8)
        }
      }
    }
    .disabled(viewModel.isClearingCache)
  }
}

#Preview {
  AppSettingsView()
}
